import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Exam } from './entities/exam.entity';
import { ExamQuestion } from './entities/exam-question.entity';
import { ExamResult } from './entities/exam-result.entity';
import { ExamResultService } from './exam-result.service';
import { ExamController } from './exam.controller';
import { ExamService } from './exam.service';
import { AuthModule } from '../auth/auth.module';
import { PermissionModule } from '../permission/permission.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Exam, ExamQuestion, ExamResult]),
    AuthModule,
    PermissionModule,
  ],
  controllers: [ExamController],
  providers: [ExamService, ExamResultService],
  exports: [ExamService],
})
export class ExamModule {}
